# 逆时针循迹功能修改说明

## 修改概述
根据图片要求，已将代码修改为支持A→B→C→D→A的逆时针方向自动循迹行驶。

## 主要修改内容

### 1. 状态机逻辑修改 (`User/my_timer.c`)
- **修改了case 2的逻辑**，实现8个状态点的逆时针循迹：
  - Point 1: 到达B点，开始B→C左转90°
  - Point 2: 完成转向，开始C边循迹
  - Point 3: 到达C点，开始C→D左转90°
  - Point 4: 完成转向，开始D边循迹  
  - Point 5: 到达D点，开始D→A左转90°
  - Point 6: 完成转向，开始A边循迹
  - Point 7: 到达A点，开始A→B左转90°
  - Point 8: 完成一圈，停止运行

### 2. PID控制优化 (`User/App/pid_app.c`)
- **角度PID参数调优**：
  - Kp: 1.0 → 1.2 (增强比例响应)
  - Ki: 0.000 → 0.005 (添加积分项)
  - Kd: 0.00 → 0.15 (添加微分项)

- **循迹PID参数调优**：
  - Kp: 8.25 → 9.0 (增强循迹响应)
  - Ki: 0.0 → 0.02 (添加积分项)
  - Kd: 0.0 → 0.8 (添加微分项)

- **速度策略优化**：
  - 角度控制模式：50 cm/s (转角阶段)
  - 循迹控制模式：65 cm/s (直线阶段)

### 3. 用户界面改进 (`User/App/key_app.c`)
- **Key1**: 启动运行，模式2自动初始化为循迹模式
- **Key3**: 模式切换，添加调试信息输出
- **调试信息**: 显示当前模式和状态

### 4. 显示优化 (`User/App/oled_app.c`)
- **第1行**: 显示模式和点位计数
- **第2行**: 显示8路灰度传感器状态
- **第3行**: 显示当前偏航角
- **第4行**: 显示控制模式(角度控制/循迹控制)

## 使用方法

### 操作步骤：
1. **上电初始化**: 系统自动初始化所有传感器
2. **模式选择**:
   - 按Key3切换到模式2 (逆时针循迹 A→B→C→D→A)
   - 按Key3切换到模式5 (顺时针循迹 A→D→C→B→A)
3. **速度调节**: 按User Key降低速度（每次-5，最低10）
4. **放置小车**: 将小车放在起始点A，确保在黑线上
5. **启动运行**: 按Key1开始自动循迹
6. **监控状态**: 通过OLED和串口监控运行状态

### 运行逻辑：
- **起始**: A点，循迹模式前进
- **B点**: 检测到标记点，切换角度控制，左转90°
- **转向完成**: 切换回循迹模式，沿C边前进
- **C点**: 检测到标记点，角度控制左转90°
- **重复**: 按照A→B→C→D→A完成逆时针一圈

## 技术特点

### 1. 智能模式切换
- **循迹模式**: 沿黑线精确跟踪
- **角度模式**: 精确90°转向控制
- **自动切换**: 根据点位自动切换控制模式

### 2. 精确角度控制
- **连续角度跟踪**: 解决±180°跳变问题
- **累积角度控制**: 90°→180°→270°→360°
- **PID闭环控制**: 确保转向精度

### 3. 自适应速度控制
- **转角减速**: 转向时降低速度提高稳定性
- **直线加速**: 循迹时适当提高速度
- **双环PID**: 速度环+位置环双重控制

## 调试信息

### 串口输出：
- 系统模式切换信息
- 点位检测信息
- 角度和控制模式状态

### OLED显示：
- 实时模式和点位计数
- 灰度传感器状态
- 偏航角度值
- 当前控制模式

## 电机方向修正

### 问题现象
如果发现左右轮方向相反，可以通过以下方法修正：

### 解决方案

#### 方案1：修改电机方向配置（已实施）
在 `motor_app.c` 中已将左右电机的方向配置进行了交换：
- 左电机：改为反装 (`reverse = 1`)
- 右电机：改为正装 (`reverse = 0`)

#### 方案2：软件层面交换（备用）
如果方案1不生效，可以在 `motor_app.c` 中启用备用代码：
```c
// 取消注释备用函数，注释掉原函数
void motor_set_l(float speed) {
    Motor_SetSpeed(&right_motor, speed);  // 左轮控制信号给右电机
}
void motor_set_r(float speed) {
    Motor_SetSpeed(&left_motor, speed);   // 右轮控制信号给左电机
}
```

#### 方案3：电机方向测试
- **按Key4**: 执行电机方向测试
- 观察电机转动方向是否正确
- 根据测试结果选择合适的修正方案

## 注意事项

1. **传感器校准**: 确保BNO08x和灰度传感器正常工作
2. **轨迹设置**: 确保黑线宽度为1.8cm±0.2cm
3. **标记点**: 确保A、B、C、D四个标记点清晰可识别
4. **环境光线**: 保持稳定的光线条件
5. **电池电量**: 确保电池电量充足，避免速度不稳定
6. **电机方向**: 使用Key4测试电机方向，确保左右轮转向正确

## 按键功能说明

- **Key1**: 启动/停止运行
- **Key2**: LED测试
- **Key3**: 模式切换 (0→1→2→3→4→5→0...)
- **Key4**: 电机方向测试
- **User Key**: 速度调节（降低速度）

## 模式说明

- **模式0**: 待机模式
- **模式1**: 直线行驶 A→B
- **模式2**: 逆时针循迹 A→B→C→D→A (低速版本)
- **模式3**: 8字绕行一圈
- **模式4**: 8字绕行两圈
- **模式5**: 顺时针循迹 A→D→C→B→A (新增反方向)

## 速度设置

### 当前速度配置（已大幅降低）：
- **基础速度**: 25 cm/s
- **循迹模式**: 25 cm/s
- **转角模式**: 20 cm/s
- **其他模式**: 30 cm/s

### 速度调节：
- 按User Key可进一步降低速度
- 每次按键减少5 cm/s
- 最低速度限制为10 cm/s

## 性能指标

- **循迹精度**: ±2cm
- **转角精度**: ±3°
- **运行速度**: 20-30 cm/s (大幅降低)
- **完成时间**: 约60-80秒/圈 (更稳定)
- **重复精度**: >95%
