#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  // 左电机：修改方向配置
  Motor_Create(&left_motor, &htim1,
               TIM_CHANNEL_1, TIM_CHANNEL_2,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_9, GPIO_AF1_TIM1,       // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_11, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               1);                                     // 改为反装

  // 右电机：修改方向配置
  Motor_Create(&right_motor, &htim1,
               TIM_CHANNEL_3, TIM_CHANNEL_4,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_13, GPIO_AF1_TIM1,      // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_14, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               0);                                     // 改为正装
}

//speed -100.0 ~ +100.0, 支持一位小数精度
// 原始函数（如果方向不对，注释掉这两个函数）
/*
void motor_set_l(float speed)
{
	Motor_SetSpeed(&left_motor, speed);
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&right_motor, speed);
}
*/

// 备用方案：交换左右轮控制（如果还不行，注释掉这个版本）
/*
void motor_set_l(float speed)
{
	Motor_SetSpeed(&right_motor, speed);  // 左轮控制信号给右电机
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&left_motor, speed);   // 右轮控制信号给左电机
}
*/

// 第三个方案：交换+反向（启用这个版本试试）
void motor_set_l(float speed)
{
	Motor_SetSpeed(&right_motor, -speed);  // 左轮控制信号给右电机，并反向
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&left_motor, -speed);   // 右轮控制信号给左电机，并反向
}

void motor_break(void)
{
	Motor_Stop(&right_motor);
	Motor_Stop(&left_motor);
}

// 电机方向测试函数
void motor_direction_test(void)
{
	extern UART_HandleTypeDef huart1;

	my_printf(&huart1, "=== Motor Direction Test ===\r\n");

	// 测试左轮正转
	my_printf(&huart1, "Testing LEFT motor forward (50%%)...\r\n");
	motor_set_l(50.0f);
	motor_set_r(0.0f);
	HAL_Delay(2000);

	// 停止
	motor_break();
	HAL_Delay(1000);

	// 测试右轮正转
	my_printf(&huart1, "Testing RIGHT motor forward (50%%)...\r\n");
	motor_set_l(0.0f);
	motor_set_r(50.0f);
	HAL_Delay(2000);

	// 停止
	motor_break();
	HAL_Delay(1000);

	// 测试双轮同向
	my_printf(&huart1, "Testing BOTH motors forward (30%%)...\r\n");
	motor_set_l(30.0f);
	motor_set_r(30.0f);
	HAL_Delay(2000);

	// 停止
	motor_break();
	my_printf(&huart1, "=== Test Complete ===\r\n");
}
