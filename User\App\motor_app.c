#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  // 左电机：修改方向配置
  Motor_Create(&left_motor, &htim1,
               TIM_CHANNEL_1, TIM_CHANNEL_2,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_9, GPIO_AF1_TIM1,       // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_11, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               1);                                     // 改为反装

  // 右电机：修改方向配置
  Motor_Create(&right_motor, &htim1,
               TIM_CHANNEL_3, TIM_CHANNEL_4,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_13, GPIO_AF1_TIM1,      // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_14, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               0);                                     // 改为正装
}

//speed -100.0 ~ +100.0, 支持一位小数精度
void motor_set_l(float speed)
{
	Motor_SetSpeed(&left_motor, speed);
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&right_motor, speed);
}

// 如果方案1不行，可以使用这个备用方案（交换左右轮控制）
// 取消注释下面的函数，并注释掉上面的函数
/*
void motor_set_l(float speed)
{
	Motor_SetSpeed(&right_motor, speed);  // 左轮控制信号给右电机
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&left_motor, speed);   // 右轮控制信号给左电机
}
*/

void motor_break(void)
{
	Motor_Stop(&right_motor);
	Motor_Stop(&left_motor);
}

// 电机方向测试函数
void motor_direction_test(void)
{
	// 测试左轮正转
	motor_set_l(50.0f);
	motor_set_r(0.0f);
	HAL_Delay(1000);

	// 停止
	motor_break();
	HAL_Delay(500);

	// 测试右轮正转
	motor_set_l(0.0f);
	motor_set_r(50.0f);
	HAL_Delay(1000);

	// 停止
	motor_break();
}
