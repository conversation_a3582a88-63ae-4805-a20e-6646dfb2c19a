#include "key_app.h"

extern UART_HandleTypeDef huart1;

extern uint8_t led_rgb[5];
extern bool pid_running; 

uint8_t runing_flat = 0;
uint16_t runing_time = 0;

uint8_t test = 0;
extern uint8_t first_flat;

extern unsigned char system_mode;

extern uint8_t stop_flat;
extern float g_last_yaw;
extern int g_revolution_count;
extern bool g_is_yaw_initialized;
void key_task(void)
{
	static uint8_t key_old = 0;
	
	uint8_t key_val = key_read();
	uint8_t key_down = key_val & (key_val ^ key_old);
//	uint8_t key_up = ~key_val & (key_val ^ key_old);
	key_old = key_val;
	
	
	switch(key_down)
	{
		case 1://Key1 - ��ʼ����
			pid_running = 1;
			stop_flat = 0;

			// ���ģʽ2����ʱ��ѭ��ģʽ����ģʽ5��˳ʱ��ѭ��ģʽ����ʼ��Ϊѭ��ģʽ
			if(system_mode == 2 || system_mode == 5)
			{
				pid_control_mode = 1; // ��ʼʱʹ��ѭ��������
				point_count = 0; // ����Ƽ���
			}

		break;
		case 2://Key2 - 简单前进测试
			led_rgb[0] = 1;
			led_rgb[1] = 0;
			led_rgb[2] = 0;

			// 简单的前进测试
			my_printf(&huart1, "Simple forward test (3 seconds)...\r\n");
			motor_set_l(25.0f);  // 左轮25%速度
			motor_set_r(25.0f);  // 右轮25%速度
			HAL_Delay(3000);     // 前进3秒
			motor_break();       // 停止
			my_printf(&huart1, "Forward test completed.\r\n");
		break;
		case 3://Key3 - ģʽ�л�
			led_rgb[0] = 0;
			led_rgb[1] = 1;
			led_rgb[2] = 1;

		  system_mode = (system_mode + 1)%6;

			// ��ӡ��ǰģʽ
			my_printf(&huart1, "System Mode: %d\r\n", system_mode);
			if(system_mode == 2)
			{
				my_printf(&huart1, "Mode 2: Counter-clockwise tracking A->B->C->D->A\r\n");
			}
			else if(system_mode == 5)
			{
				my_printf(&huart1, "Mode 5: Clockwise tracking A->D->C->B->A\r\n");
			}



		break;
		case 4://Key4 - 电机方向测试
			my_printf(&huart1, "Motor Direction Test Starting...\r\n");
			my_printf(&huart1, "Left motor forward...\r\n");
			motor_direction_test();
			my_printf(&huart1, "Motor Direction Test Completed!\r\n");
		break;
		case 10://User Key - 速度调节
			// 降低基础速度
			if(basic_speed > 10)
			{
				basic_speed -= 5;
				my_printf(&huart1, "Speed decreased to: %d\r\n", basic_speed);
			}
			else
			{
				my_printf(&huart1, "Speed already at minimum: %d\r\n", basic_speed);
			}
		break;
	}
}
