#include "my_timer.h"

extern int basic_speed;

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int output_timer500ms = 0;

unsigned char intput_ff_flag;
unsigned int intput_timer500ms;

unsigned int led_timer500ms; // ??????????LED ???? 500ms ?????

unsigned char point_count = 0; // ???????��????????? + 1????? + 1??

unsigned char system_mode = 1; // ??????1 ~ 4 ??? 4 ???????

unsigned char circle_count = 0; // ????????????????

unsigned int distance = 0; // ???��??????????????

extern uint8_t led_rgb[5];

void timer_init(void)
{
  HAL_TIM_Base_Start_IT(&htim2);
}

// TIM2 ?��????????1ms ?��??
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == htim2.Instance)
	{
		if(++measure_timer5ms >= 5)
		{
			measure_timer5ms = 0;
			
			Encoder_Task();
			distance += left_encoder.speed_cm_s;
			Gray_Task();
			bno080_task();
			PID_Task();
		}
		
//		if(pid_running != 1) return;
		
		/* ????????? */
		if(Digtal != 0x00)
		{
			output_ff_flag = 1;
			if(++intput_timer500ms >= 500) intput_timer500ms = 500;
		}
		else if(output_ff_flag == 1 && intput_timer500ms == 500)
		{
			output_ff_flag = 0;
			intput_timer500ms = 0;
			point_count++;
			Car_State_Update();
		}
////		
////	  /* ????????? */
	  if(Digtal == 0x00)
	  {
	    intput_ff_flag = 1;
	    if(++output_timer500ms >= 500) output_timer500ms = 500;
	  }
	  else if(intput_ff_flag == 1 && output_timer500ms == 500)
	  {
	    intput_ff_flag = 0;
	    output_timer500ms = 0;
	    point_count++;
	    Car_State_Update();
	  }
		
		
		/* LED ????? */
	  if(led_rgb[0] == 1 && ++led_timer500ms >= 500)
	  {
	    led_rgb[0] = 0;
	    led_timer500ms = 0;
	  }
	}
}

extern uint8_t stop_flat;
//??��?��??????????????????????????��??????
void Car_State_Update(void)
{
  led_rgb[0] = 1;
  distance = 0;

  // ?????��?????
  extern UART_HandleTypeDef huart1;
  my_printf(&huart1, "Point Count: %d, System Mode: %d\r\n", point_count, system_mode);
  
  switch(system_mode)
  {
    case 1: // ??????????? A -> B
      if(point_count == 1)
      {
//        pid_running = 0;
////        motor_break();
				point_count = 0;
				stop_flat = 1;
      }
      break;
    case 2: // ?????????????? A -> B -> C -> D -> A
      if(point_count == 1) // ????B?????B->C???
      {
        pid_control_mode = 0; // ?????????????????90??
        pid_set_target(&pid_angle, 90); // ????????90??
      }
      else if(point_count == 2) // ???B->C?????C?????
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 3) // ????C?????C->D???
      {
        pid_control_mode = 0; // ?????????????????90??
        pid_set_target(&pid_angle, 180); // ??????180??
      }
      else if(point_count == 4) // ???C->D?????D?????
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 5) // ????D?????D->A???
      {
        pid_control_mode = 0; // ?????????????????90??
        pid_set_target(&pid_angle, 270); // ??????270??
      }
      else if(point_count == 6) // ???D->A?????A?????
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 7) // ????A?????A->B???
      {
        pid_control_mode = 0; // ?????????????????90??
        pid_set_target(&pid_angle, 360); // ??????360??????????
      }
      else if(point_count == 8) // ?????????
      {
        stop_flat = 1;
        point_count = 0; // ?????????????????
      }
      break;
    case 3: // ??????8 ??????? A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ??????????
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1;
      }
      break;
    case 4: // ??????8 ????????
      if(point_count == 1)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ??????????
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ????????????
      else if(point_count == 4)
      {
				pid_set_target(&pid_angle, 36);
        if(++circle_count >= 4)
        {
//          pid_running = 0;
//          motor_break();
					stop_flat = 1;
        }
        point_count = 0;
        pid_control_mode = 0; // ??????????
      }
      break;
    case 5: // ˳ʱ�뷽��ѭ������ A -> D -> C -> B -> A (��������)
      if(point_count == 1) // ����D�㣬׼��D->Cת��
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����������ת90��
        pid_set_target(&pid_angle, -90); // ˳ʱ����ת90��
      }
      else if(point_count == 2) // ���D->Cת�򣬿�ʼC��ѭ��
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 3) // ����C�㣬׼��C->Bת��
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����������ת90��
        pid_set_target(&pid_angle, -180); // �ۼ���ת180��
      }
      else if(point_count == 4) // ���C->Bת�򣬿�ʼB��ѭ��
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 5) // ����B�㣬׼��B->Aת��
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����������ת90��
        pid_set_target(&pid_angle, -270); // �ۼ���ת270��
      }
      else if(point_count == 6) // ���B->Aת�򣬿�ʼA��ѭ��
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 7) // ����A�㣬׼��A->Dת��
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����������ת90��
        pid_set_target(&pid_angle, -360); // �ۼ���ת360�ȣ������һȦ
      }
      else if(point_count == 8) // ���һȦ��ֹͣ
      {
        stop_flat = 1;
        point_count = 0; // ����ƣ���ѡ�����ѭ��
      }
      break;
  }
  
  /* ?????????? */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
